<script setup lang="ts">
import { onMounted, onUpdated, ref } from 'vue';
import hljs from 'highlight.js';
import { useDrawerDirection } from '@/composables/useDrawerDirection';
// import 'highlight.js/styles/foundation.css';

const drawerVisible = ref(false);
const note = ref('');

// 使用动态方向
const { drawerDirection } = useDrawerDirection();

// 展示drawer
const showDrawer = (data: any) => {
  drawerVisible.value = true;
  note.value = data;
};

defineExpose({
  showDrawer
});

// 高亮代码
const highlightCode = () => {
  hljs.highlightAll();
};

onMounted(highlightCode);
onUpdated(highlightCode);
</script>
<template>
  <el-drawer
    v-model="drawerVisible"
    :direction="drawerDirection"
    size="740px"
    class="drawer hover-scrollbar"
  >
    <template #header>
      <div class="header-text">
        编者笔记
        <div style="margin-top: 5px" class="line"></div>
      </div>
    </template>

    <div class="wrapper NoteDrawer">
      <span v-if="!note">暂无内容</span>
      <span v-else >
        <ContentRenderer :content="note"></ContentRenderer>
      </span>
    </div>
  </el-drawer>
</template>
<style scoped>
.header-text {
  color: var(--color-black);
  font-weight: 600;
  /* word-break: break-all; */
  position: relative;
}
.wrapper {
  font-size: 14px;
  color: var(--color-black);
  overflow-y: auto !important;
}
.line {
  width: 140%;
  height: 1px;
  background-color: var(--color-boxborder);
  position: absolute;
  bottom: -10px;
  left: -20px;
}

/* 移除 el-drawer 内部的默认 padding */
:deep(.el-drawer__body) {
  padding: 0 !important;
  overflow: hidden;
}
/* 更强的选择器优先级 */
.el-drawer .wrapper {
  max-height: calc(100vh - 120px);
  overflow-y: auto !important;
}
:deep(.el-drawer__header) {
  padding: 20px 20px 0 20px;
  margin: 0;
}
</style>
